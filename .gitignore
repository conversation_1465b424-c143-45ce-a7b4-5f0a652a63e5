# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# dependencies
node_modules
.pnp
.pnp.js

# testing
coverage

# production
.next
.swc
_static
out
dist
build

# environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# misc
.DS_Store
.vercel
.netlify
.vscode
tsconfig.tsbuildinfo
.ncurc.js

.cursor
private
.github/instructions*
.augment-guidelines

# clerk configuration (can include secrets)
/.clerk/
