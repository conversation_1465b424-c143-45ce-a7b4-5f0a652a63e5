import type { UserProfile } from 'src/stores/user-store';

import { useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z as zod } from 'zod';

import Box from '@mui/material/Box';
import Alert from '@mui/material/Alert';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import MenuItem from '@mui/material/MenuItem';
import LoadingButton from '@mui/lab/LoadingButton';
import DialogTitle from '@mui/material/DialogTitle';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';

import { useUserStore, UserStatus, UserRoles } from 'src/stores/user-store';

import { toast } from 'src/components/snackbar';
import { Form, Field } from 'src/components/hook-form';

// ----------------------------------------------------------------------

export const AdminUserQuickEditSchema = zod.object({
  displayName: zod.string().min(1, { message: 'Display name is required!' }),
  email: zod
    .string()
    .min(1, { message: 'Email is required!' })
    .email({ message: 'Email must be a valid email address!' }),
  phoneNumber: zod.string().optional(),
  role: zod.nativeEnum(UserRoles),
  status: zod.nativeEnum(UserStatus),
  isVerified: zod.boolean(),
});

export type AdminUserQuickEditSchemaType = zod.infer<typeof AdminUserQuickEditSchema>;

// ----------------------------------------------------------------------

type Props = {
  currentUser?: UserProfile;
  open: boolean;
  onClose: () => void;
};

export function AdminUserQuickEditForm({ currentUser, open, onClose }: Props) {
  const { updateUser } = useUserStore();

  const defaultValues = useMemo(
    () => ({
      displayName: currentUser?.displayName || '',
      email: currentUser?.email || '',
      phoneNumber: currentUser?.phoneNumber || '',
      role: currentUser?.role || UserRoles.USER,
      status: currentUser?.status || UserStatus.ACTIVE,
      isVerified: currentUser?.isVerified || false,
    }),
    [currentUser]
  );

  const methods = useForm<AdminUserQuickEditSchemaType>({
    mode: 'all',
    resolver: zodResolver(AdminUserQuickEditSchema),
    defaultValues,
  });

  const {
    reset,
    handleSubmit,
    formState: { isSubmitting },
  } = methods;

  const onSubmit = handleSubmit(async (data) => {
    try {
      if (!currentUser) return;

      updateUser(currentUser.userId, {
        displayName: data.displayName,
        email: data.email,
        phoneNumber: data.phoneNumber,
        role: data.role,
        status: data.status,
        isVerified: data.isVerified,
      });

      toast.success('User updated successfully!');
      onClose();
      reset();
    } catch (error) {
      console.error('Error updating user:', error);
      toast.error('Failed to update user');
    }
  });

  return (
    <Dialog
      fullWidth
      maxWidth="xs"
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: { overflow: 'visible' },
      }}
    >
      <Form methods={methods} onSubmit={onSubmit}>
        <DialogTitle>Quick Update</DialogTitle>

        <DialogContent>
          <Alert variant="outlined" severity="info" sx={{ mb: 3 }}>
            Account is waiting for confirmation
          </Alert>

          <Box
            rowGap={3}
            columnGap={2}
            display="grid"
            gridTemplateColumns={{
              xs: 'repeat(1, 1fr)',
              sm: 'repeat(2, 1fr)',
            }}
          >
            <Field.Text name="displayName" label="Display Name" />
            <Field.Text name="email" label="Email Address" />
            <Field.Text name="phoneNumber" label="Phone Number" />

            <Field.Select name="status" label="Status">
              {Object.values(UserStatus).map((status) => (
                <MenuItem key={status} value={status}>
                  {status.charAt(0).toUpperCase() + status.slice(1)}
                </MenuItem>
              ))}
            </Field.Select>

            <Field.Select name="role" label="Role">
              {Object.values(UserRoles).map((role) => (
                <MenuItem key={role} value={role}>
                  {role.charAt(0).toUpperCase() + role.slice(1)}
                </MenuItem>
              ))}
            </Field.Select>

            <Field.Switch name="isVerified" label="Email Verified" />
          </Box>
        </DialogContent>

        <DialogActions>
          <Button variant="outlined" onClick={onClose}>
            Cancel
          </Button>

          <LoadingButton type="submit" variant="contained" loading={isSubmitting}>
            Update
          </LoadingButton>
        </DialogActions>
      </Form>
    </Dialog>
  );
}
