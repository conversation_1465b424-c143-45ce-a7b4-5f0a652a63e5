import type { UserTableFilters } from 'src/stores/user-store';
import type { UseSetStateReturn } from 'minimal-shared/hooks';
import type { Theme, SxProps } from '@mui/material/styles';

import { useCallback } from 'react';

import Box from '@mui/material/Box';
import Chip from '@mui/material/Chip';
import Paper from '@mui/material/Paper';
import Button from '@mui/material/Button';
import Stack, { StackProps } from '@mui/material/Stack';

import { useUserStore } from 'src/stores/user-store';

import { Iconify } from 'src/components/iconify';

// ----------------------------------------------------------------------

type Props = StackProps & {
  filters: UseSetStateReturn<UserTableFilters>;
  totalResults: number;
  onResetPage: () => void;
  sx?: SxProps<Theme>;
};

export function AdminUsersTableFiltersResult({
  filters,
  totalResults,
  onResetPage,
  sx,
  ...other
}: Props) {
  const { setFilters, resetFilters } = useUserStore();
  const { state: currentFilters, setState: updateFilters } = filters;

  const handleRemoveKeyword = useCallback(() => {
    const updatedFilters = { ...currentFilters, search: '' };
    updateFilters(updatedFilters);
    setFilters(updatedFilters);
    onResetPage();
  }, [currentFilters, updateFilters, setFilters, onResetPage]);

  const handleRemoveClkkId = useCallback(() => {
    const updatedFilters = { ...currentFilters, clkk_id: '' };
    updateFilters(updatedFilters);
    setFilters(updatedFilters);
    onResetPage();
  }, [currentFilters, updateFilters, setFilters, onResetPage]);

  const handleRemoveEmail = useCallback(() => {
    const updatedFilters = { ...currentFilters, email: '' };
    updateFilters(updatedFilters);
    setFilters(updatedFilters);
    onResetPage();
  }, [currentFilters, updateFilters, setFilters, onResetPage]);

  const handleRemoveStatus = useCallback(() => {
    const updatedFilters = { ...currentFilters, status: 'all' };
    updateFilters(updatedFilters);
    setFilters(updatedFilters);
    onResetPage();
  }, [currentFilters, updateFilters, setFilters, onResetPage]);

  const handleRemoveRole = useCallback(
    (inputValue: string) => {
      const newValue = currentFilters.role.filter((item) => item !== inputValue);
      const updatedFilters = { ...currentFilters, role: newValue };
      updateFilters(updatedFilters);
      setFilters(updatedFilters);
      onResetPage();
    },
    [currentFilters, updateFilters, setFilters, onResetPage]
  );

  const handleReset = useCallback(() => {
    resetFilters();
    updateFilters({
      search: '',
      role: [],
      status: 'all',
      clkk_id: '',
      email: '',
    });
    onResetPage();
  }, [resetFilters, updateFilters, onResetPage]);

  return (
    <Stack spacing={1.5} sx={sx} {...other}>
      <Box sx={{ typography: 'body2' }}>
        <strong>{totalResults}</strong>
        <Box component="span" sx={{ color: 'text.secondary', ml: 0.25 }}>
          results found
        </Box>
      </Box>

      <Stack flexGrow={1} spacing={1} direction="row" flexWrap="wrap" alignItems="center">
        {!!currentFilters.search && (
          <Block label="Keyword:">
            <Chip size="small" label={currentFilters.search} onDelete={handleRemoveKeyword} />
          </Block>
        )}

        {!!currentFilters.clkk_id && (
          <Block label="CLKK ID:">
            <Chip size="small" label={currentFilters.clkk_id} onDelete={handleRemoveClkkId} />
          </Block>
        )}

        {!!currentFilters.email && (
          <Block label="Email:">
            <Chip size="small" label={currentFilters.email} onDelete={handleRemoveEmail} />
          </Block>
        )}

        {currentFilters.status !== 'all' && (
          <Block label="Status:">
            <Chip size="small" label={currentFilters.status} onDelete={handleRemoveStatus} />
          </Block>
        )}

        {!!currentFilters.role.length && (
          <Block label="Role:">
            {currentFilters.role.map((item) => (
              <Chip
                key={item}
                label={item}
                size="small"
                onDelete={() => handleRemoveRole(item)}
              />
            ))}
          </Block>
        )}

        <Button
          color="error"
          onClick={handleReset}
          startIcon={<Iconify icon="solar:trash-bin-trash-bold" />}
        >
          Clear
        </Button>
      </Stack>
    </Stack>
  );
}

// ----------------------------------------------------------------------

type BlockProps = {
  label: string;
  children: React.ReactNode;
};

function Block({ label, children }: BlockProps) {
  return (
    <Stack component={Paper} variant="outlined" spacing={1} direction="row" sx={{ p: 1 }}>
      <Box component="span" sx={{ typography: 'subtitle2' }}>
        {label}
      </Box>

      <Stack spacing={1} direction="row" flexWrap="wrap">
        {children}
      </Stack>
    </Stack>
  );
}
