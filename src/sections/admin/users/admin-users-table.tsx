'use client';

import type { TableHeadCellProps } from 'src/components/table';
import type { UserProfile, UserTableFilters } from 'src/stores/user-store';

import { useState, useCallback } from 'react';
import { varAlpha } from 'minimal-shared/utils';
import { useBoolean, useSetState } from 'minimal-shared/hooks';

import Box from '@mui/material/Box';
import Tab from '@mui/material/Tab';
import Tabs from '@mui/material/Tabs';
import Card from '@mui/material/Card';
import Table from '@mui/material/Table';
import Button from '@mui/material/Button';
import Tooltip from '@mui/material/Tooltip';
import TableBody from '@mui/material/TableBody';
import IconButton from '@mui/material/IconButton';

import { useUserStore, UserStatus, UserRoles } from 'src/stores/user-store';

import { Label } from 'src/components/label';
import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';
import { Scrollbar } from 'src/components/scrollbar';
import { ConfirmDialog } from 'src/components/custom-dialog';
import {
  useTable,
  emptyRows,
  rowInPage,
  TableNoData,
  getComparator,
  TableEmptyRows,
  TableHeadCustom,
  TableSelectedAction,
  TablePaginationCustom,
} from 'src/components/table';

import { AdminUsersTableRow } from './admin-users-table-row';
import { AdminUsersTableToolbar } from './admin-users-table-toolbar';
import { AdminUsersTableFiltersResult } from './admin-users-table-filters-result';

// ----------------------------------------------------------------------

const STATUS_OPTIONS = [
  { value: 'all', label: 'All' },
  { value: UserStatus.ACTIVE, label: 'Active' },
  { value: UserStatus.PENDING, label: 'Pending' },
  { value: UserStatus.INACTIVE, label: 'Inactive' },
  { value: UserStatus.BANNED, label: 'Banned' },
  { value: UserStatus.REJECTED, label: 'Rejected' },
];

const ROLE_OPTIONS = [
  { value: UserRoles.USER, label: 'User' },
  { value: UserRoles.AGENT, label: 'Agent' },
  { value: UserRoles.ADMIN, label: 'Admin' },
];

const TABLE_HEAD: TableHeadCellProps[] = [
  { id: 'displayName', label: 'Name' },
  { id: 'email', label: 'Email', width: 220 },
  { id: 'clkk_id', label: 'CLKK ID', width: 140 },
  { id: 'role', label: 'Role', width: 120 },
  { id: 'status', label: 'Status', width: 120 },
  { id: 'isVerified', label: 'Verified', width: 100 },
  { id: 'createdAt', label: 'Created', width: 140 },
  { id: '', width: 88 },
];

// ----------------------------------------------------------------------

export function AdminUsersTable() {
  const table = useTable();
  const confirmDialog = useBoolean();

  const { 
    users, 
    filters, 
    selectedUsers,
    deleteUser, 
    deleteUsers, 
    setSelectedUsers, 
    setFilters,
    getFilteredUsers 
  } = useUserStore();

  const [localFilters, setLocalFilters] = useSetState<UserTableFilters>(filters);

  const dataFiltered = getFilteredUsers();
  const dataInPage = rowInPage(dataFiltered, table.page, table.rowsPerPage);

  const canReset = 
    !!localFilters.search || 
    localFilters.role.length > 0 || 
    localFilters.status !== 'all' ||
    !!localFilters.clkk_id ||
    !!localFilters.email;

  const notFound = (!dataFiltered.length && canReset) || !dataFiltered.length;

  const handleDeleteRow = useCallback(
    (userId: string) => {
      deleteUser(userId);
      toast.success('User deleted successfully!');
      table.onUpdatePageDeleteRow(dataInPage.length);
    },
    [deleteUser, dataInPage.length, table]
  );

  const handleDeleteRows = useCallback(() => {
    deleteUsers(selectedUsers);
    toast.success('Users deleted successfully!');
    setSelectedUsers([]);
    table.onUpdatePageDeleteRows(dataInPage.length, dataFiltered.length);
  }, [deleteUsers, selectedUsers, setSelectedUsers, dataInPage.length, dataFiltered.length, table]);

  const handleFilterStatus = useCallback(
    (event: React.SyntheticEvent, newValue: string) => {
      table.onResetPage();
      const updatedFilters = { ...localFilters, status: newValue };
      setLocalFilters(updatedFilters);
      setFilters(updatedFilters);
    },
    [localFilters, setLocalFilters, setFilters, table]
  );

  const handleSelectRow = useCallback(
    (userId: string) => {
      const newSelected = selectedUsers.includes(userId)
        ? selectedUsers.filter(id => id !== userId)
        : [...selectedUsers, userId];
      setSelectedUsers(newSelected);
    },
    [selectedUsers, setSelectedUsers]
  );

  const handleSelectAllRows = useCallback(
    (checked: boolean) => {
      if (checked) {
        const newSelected = dataFiltered.map(user => user.userId);
        setSelectedUsers(newSelected);
      } else {
        setSelectedUsers([]);
      }
    },
    [dataFiltered, setSelectedUsers]
  );

  const renderConfirmDialog = () => (
    <ConfirmDialog
      open={confirmDialog.value}
      onClose={confirmDialog.onFalse}
      title="Delete Users"
      content={
        <>
          Are you sure you want to delete <strong>{selectedUsers.length}</strong> user(s)?
        </>
      }
      action={
        <Button
          variant="contained"
          color="error"
          onClick={() => {
            handleDeleteRows();
            confirmDialog.onFalse();
          }}
        >
          Delete
        </Button>
      }
    />
  );

  return (
    <>
      <Card>
        <Tabs
          value={localFilters.status}
          onChange={handleFilterStatus}
          sx={[
            (theme) => ({
              px: 2.5,
              boxShadow: `inset 0 -2px 0 0 ${varAlpha(theme.vars.palette.grey['500Channel'], 0.08)}`,
            }),
          ]}
        >
          {STATUS_OPTIONS.map((tab) => (
            <Tab
              key={tab.value}
              iconPosition="end"
              value={tab.value}
              label={tab.label}
              icon={
                <Label
                  variant={
                    ((tab.value === 'all' || tab.value === localFilters.status) && 'filled') ||
                    'soft'
                  }
                  color={
                    (tab.value === UserStatus.ACTIVE && 'success') ||
                    (tab.value === UserStatus.PENDING && 'warning') ||
                    (tab.value === UserStatus.BANNED && 'error') ||
                    (tab.value === UserStatus.INACTIVE && 'default') ||
                    (tab.value === UserStatus.REJECTED && 'error') ||
                    'default'
                  }
                >
                  {tab.value === 'all'
                    ? users.length
                    : users.filter((user) => user.status === tab.value).length}
                </Label>
              }
            />
          ))}
        </Tabs>

        <AdminUsersTableToolbar
          filters={{ state: localFilters, setState: setLocalFilters }}
          onResetPage={table.onResetPage}
          options={{ roles: ROLE_OPTIONS }}
        />

        {canReset && (
          <AdminUsersTableFiltersResult
            filters={{ state: localFilters, setState: setLocalFilters }}
            totalResults={dataFiltered.length}
            onResetPage={table.onResetPage}
            sx={{ p: 2.5, pt: 0 }}
          />
        )}

        <Box sx={{ position: 'relative' }}>
          <TableSelectedAction
            dense={table.dense}
            numSelected={selectedUsers.length}
            rowCount={dataFiltered.length}
            onSelectAllRows={handleSelectAllRows}
            action={
              <Tooltip title="Delete">
                <IconButton color="primary" onClick={confirmDialog.onTrue}>
                  <Iconify icon="solar:trash-bin-trash-bold" />
                </IconButton>
              </Tooltip>
            }
          />

          <Scrollbar>
            <Table size={table.dense ? 'small' : 'medium'} sx={{ minWidth: 960 }}>
              <TableHeadCustom
                order={table.order}
                orderBy={table.orderBy}
                headCells={TABLE_HEAD}
                rowCount={dataFiltered.length}
                numSelected={selectedUsers.length}
                onSort={table.onSort}
                onSelectAllRows={handleSelectAllRows}
              />

              <TableBody>
                {dataFiltered
                  .slice(
                    table.page * table.rowsPerPage,
                    table.page * table.rowsPerPage + table.rowsPerPage
                  )
                  .map((row) => (
                    <AdminUsersTableRow
                      key={row.userId}
                      row={row}
                      selected={selectedUsers.includes(row.userId)}
                      onSelectRow={() => handleSelectRow(row.userId)}
                      onDeleteRow={() => handleDeleteRow(row.userId)}
                    />
                  ))}

                <TableEmptyRows
                  height={table.dense ? 56 : 56 + 20}
                  emptyRows={emptyRows(table.page, table.rowsPerPage, dataFiltered.length)}
                />

                <TableNoData notFound={notFound} />
              </TableBody>
            </Table>
          </Scrollbar>
        </Box>

        <TablePaginationCustom
          page={table.page}
          dense={table.dense}
          count={dataFiltered.length}
          rowsPerPage={table.rowsPerPage}
          onPageChange={table.onChangePage}
          onChangeDense={table.onChangeDense}
          onRowsPerPageChange={table.onChangeRowsPerPage}
        />
      </Card>

      {renderConfirmDialog()}
    </>
  );
}
