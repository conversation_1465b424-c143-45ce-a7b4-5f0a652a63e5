import type { UserTableFilters } from 'src/stores/user-store';
import type { UseSetStateReturn } from 'minimal-shared/hooks';

import { useCallback } from 'react';

import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import TextField from '@mui/material/TextField';
import InputAdornment from '@mui/material/InputAdornment';

import { useUserStore } from 'src/stores/user-store';

import { Iconify } from 'src/components/iconify';

// ----------------------------------------------------------------------

type Props = {
  filters: UseSetStateReturn<UserTableFilters>;
  onResetPage: () => void;
  options: {
    roles: { value: string; label: string }[];
  };
};

export function AdminUsersTableToolbar({ filters, onResetPage, options }: Props) {
  const { setFilters } = useUserStore();
  const { state: currentFilters, setState: updateFilters } = filters;

  const handleFilterSearch = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const value = event.target.value;
      const updatedFilters = { ...currentFilters, search: value };
      updateFilters(updatedFilters);
      setFilters(updatedFilters);
      onResetPage();
    },
    [currentFilters, updateFilters, setFilters, onResetPage]
  );

  const handleFilterClkkId = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const value = event.target.value;
      const updatedFilters = { ...currentFilters, clkk_id: value };
      updateFilters(updatedFilters);
      setFilters(updatedFilters);
      onResetPage();
    },
    [currentFilters, updateFilters, setFilters, onResetPage]
  );

  const handleFilterEmail = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const value = event.target.value;
      const updatedFilters = { ...currentFilters, email: value };
      updateFilters(updatedFilters);
      setFilters(updatedFilters);
      onResetPage();
    },
    [currentFilters, updateFilters, setFilters, onResetPage]
  );

  return (
    <Stack
      spacing={2}
      alignItems={{ xs: 'flex-end', md: 'center' }}
      direction={{ xs: 'column', md: 'row' }}
      sx={{ p: 2.5 }}
    >
      <Box
        sx={{
          display: 'grid',
          columnGap: 2,
          rowGap: 2.5,
          gridTemplateColumns: {
            xs: 'repeat(1, 1fr)',
            sm: 'repeat(2, 1fr)',
            md: 'repeat(3, 1fr)',
          },
        }}
      >
        <TextField
          fullWidth
          value={currentFilters.search}
          onChange={handleFilterSearch}
          placeholder="Search users..."
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Iconify icon="eva:search-fill" sx={{ color: 'text.disabled' }} />
              </InputAdornment>
            ),
          }}
        />

        <TextField
          fullWidth
          value={currentFilters.clkk_id}
          onChange={handleFilterClkkId}
          placeholder="Search by CLKK ID..."
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Iconify icon="mdi:identifier" sx={{ color: 'text.disabled' }} />
              </InputAdornment>
            ),
          }}
        />

        <TextField
          fullWidth
          value={currentFilters.email}
          onChange={handleFilterEmail}
          placeholder="Search by email..."
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Iconify icon="eva:email-fill" sx={{ color: 'text.disabled' }} />
              </InputAdornment>
            ),
          }}
        />
      </Box>
    </Stack>
  );
}
